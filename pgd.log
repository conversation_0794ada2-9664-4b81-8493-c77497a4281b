This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.3) (preloaded format=pdflatex 2025.3.13)  29 JUL 2025 04:24
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./pgd.tex
(pgd.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\report.cls
Document Class: report 2024/06/29 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@chapter=\count197
\c@section=\count198
\c@subsection=\count199
\c@subsubsection=\count266
\c@paragraph=\count267
\c@subparagraph=\count268
\c@figure=\count269
\c@table=\count270
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count271
\Gm@cntv=\count272
\c@Gm@tempcnt=\count273
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box52
\beforetitleunit=\skip51
\aftertitleunit=\skip52
\ttl@plus=\dimen150
\ttl@minus=\dimen151
\ttl@toksa=\toks19
\titlewidth=\dimen152
\titlewidthlast=\dimen153
\titlewidthfirst=\dimen154
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tocloft\tocloft.sty
Package: tocloft 2017/08/31 v2.3i parameterised ToC, etc., typesetting
Package tocloft Info: The document has chapter divisions on input line 51.
\cftparskip=\skip53
\cftbeforetoctitleskip=\skip54
\cftaftertoctitleskip=\skip55
\cftbeforepartskip=\skip56
\cftpartnumwidth=\skip57
\cftpartindent=\skip58
\cftbeforechapskip=\skip59
\cftchapindent=\skip60
\cftchapnumwidth=\skip61
\cftbeforesecskip=\skip62
\cftsecindent=\skip63
\cftsecnumwidth=\skip64
\cftbeforesubsecskip=\skip65
\cftsubsecindent=\skip66
\cftsubsecnumwidth=\skip67
\cftbeforesubsubsecskip=\skip68
\cftsubsubsecindent=\skip69
\cftsubsubsecnumwidth=\skip70
\cftbeforeparaskip=\skip71
\cftparaindent=\skip72
\cftparanumwidth=\skip73
\cftbeforesubparaskip=\skip74
\cftsubparaindent=\skip75
\cftsubparanumwidth=\skip76
\cftbeforeloftitleskip=\skip77
\cftafterloftitleskip=\skip78
\cftbeforefigskip=\skip79
\cftfigindent=\skip80
\cftfignumwidth=\skip81
\c@lofdepth=\count274
\c@lotdepth=\count275
\cftbeforelottitleskip=\skip82
\cftafterlottitleskip=\skip83
\cftbeforetabskip=\skip84
\cfttabindent=\skip85
\cfttabnumwidth=\skip86
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip87

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen155
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen156
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count276
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count277
\leftroot@=\count278
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count279
\DOTSCASE@=\count280
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen157
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count281
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count282
\dotsspace@=\muskip17
\c@parentequation=\count283
\dspbrk@lvl=\count284
\tag@help=\toks21
\row@=\count285
\column@=\count286
\maxfields@=\count287
\andhelp@=\toks22
\eqnshift@=\dimen158
\alignsep@=\dimen159
\tagshift@=\dimen160
\tagwidth@=\dimen161
\totwidth@=\dimen162
\lineht@=\dimen163
\@envbody=\toks23
\multlinegap=\skip88
\multlinetaggap=\skip89
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amscls\amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks25
\thm@bodyfont=\toks26
\thm@headfont=\toks27
\thm@notefont=\toks28
\thm@headpunct=\toks29
\thm@preskip=\skip90
\thm@postskip=\skip91
\thm@headsep=\skip92
\dth@everypar=\toks30
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/wasysym\wasysym.sty
Package: wasysym 2020/01/19 v2.4 Wasy-2 symbol support package
\symwasy=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `wasy' in version `bold'
(Font)                  U/wasy/m/n --> U/wasy/b/n on input line 93.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
)
\Gin@req@height=\dimen164
\Gin@req@width=\dimen165
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen166
\captionmargin=\dimen167
\caption@leftmargin=\dimen168
\caption@rightmargin=\dimen169
\caption@width=\dimen170
\caption@indent=\dimen171
\caption@parindent=\dimen172
\caption@hangindent=\dimen173
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count288
\c@continuedfloat=\count289
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count290
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count291
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count292
\float@exts=\toks31
\float@box=\box55
\@float@everytoks=\toks32
\@floatcapt=\box56
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen174
\lightrulewidth=\dimen175
\cmidrulewidth=\dimen176
\belowrulesep=\dimen177
\belowbottomsep=\dimen178
\aboverulesep=\dimen179
\abovetopsep=\dimen180
\cmidrulesep=\dimen181
\cmidrulekern=\dimen182
\defaultaddspace=\dimen183
\@cmidla=\count293
\@cmidlb=\count294
\@aboverulesep=\dimen184
\@belowrulesep=\dimen185
\@thisruleclass=\count295
\@lastruleclass=\count296
\@thisrulewidth=\dimen186
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip93
\multirow@cntb=\count297
\multirow@dima=\skip94
\bigstrutjot=\dimen187
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/algorithms\algorithm.sty
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating enviro
nment

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
)
\@float@every@algorithm=\toks33
\c@algorithm=\count298
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/algorithms\algorithmic.st
y
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
\c@ALC@unique=\count299
\c@ALC@line=\count300
\c@ALC@rem=\count301
\c@ALC@depth=\count302
\ALC@tlm=\skip95
\algorithmicindent=\skip96
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\listings.sty
\lst@mode=\count303
\lst@gtempboxa=\box57
\lst@token=\toks34
\lst@length=\count304
\lst@currlwidth=\dimen188
\lst@column=\count305
\lst@pos=\count306
\lst@lostspace=\dimen189
\lst@width=\dimen190
\lst@newlines=\count307
\lst@lineno=\count308
\lst@maxwidth=\dimen191

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count309
\lst@skipnumbers=\count310
\lst@framebox=\box58
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgfplots\pgfplots.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.revis
ion.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/frontendlayer\tikz.st
y (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgf.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfrcs.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil-c
ommon.tex
\pgfutil@everybye=\toks35
\pgfutil@tempdima=\dimen192
\pgfutil@tempdimb=\dimen193
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil-l
atex.def
\pgfutil@abb=\box59
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfrcs.co
de.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgfcore.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/systemlayer\pgfsys.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsys.
code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys.c
ode.tex
\pgfkeys@pathtoks=\toks36
\pgfkeys@temptoks=\toks37

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeysli
braryfiltered.code.tex
\pgfkeys@tmptoks=\toks38
))
\pgf@x=\dimen194
\pgf@y=\dimen195
\pgf@xa=\dimen196
\pgf@ya=\dimen197
\pgf@xb=\dimen198
\pgf@yb=\dimen199
\pgf@xc=\dimen256
\pgf@yc=\dimen257
\pgf@xd=\dimen258
\pgf@yd=\dimen259
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count311
\c@pgf@countb=\count312
\c@pgf@countc=\count313
\c@pgf@countd=\count314
\t@pgf@toka=\toks39
\t@pgf@tokb=\toks40
\t@pgf@tokc=\toks41
\pgf@sys@id@count=\count315

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsys-
pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsys-
common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsyss
oftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count316
\pgfsyssoftpath@bigbuffer@items=\count317
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsysp
rotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcore.
code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code.t
ex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathutil.co
de.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathparser.
code.tex
\pgfmath@dimen=\dimen260
\pgfmath@count=\count318
\pgfmath@box=\box60
\pgfmath@toks=\toks42
\pgfmath@stack@operand=\toks43
\pgfmath@stack@operation=\toks44
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.basic.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.trigonometric.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.random.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.comparison.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.base.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.round.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.misc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunctio
ns.integerarithmetics.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathcalc.co
de.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfloat.c
ode.tex
\c@pgfmathroundto@lastzeros=\count319
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfint.code.te
x)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorep
oints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen261
\pgf@picmaxx=\dimen262
\pgf@picminy=\dimen263
\pgf@picmaxy=\dimen264
\pgf@pathminx=\dimen265
\pgf@pathmaxx=\dimen266
\pgf@pathminy=\dimen267
\pgf@pathmaxy=\dimen268
\pgf@xx=\dimen269
\pgf@xy=\dimen270
\pgf@yx=\dimen271
\pgf@yy=\dimen272
\pgf@zx=\dimen273
\pgf@zy=\dimen274
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorep
athconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen275
\pgf@path@lasty=\dimen276
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorep
athusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen277
\pgf@shorten@start@additional=\dimen278
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcores
copes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box61
\pgf@hbox=\box62
\pgf@layerbox@main=\box63
\pgf@picture@serial@count=\count320
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoreg
raphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen279
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoret
ransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen280
\pgf@pt@y=\dimen281
\pgf@pt@temp=\dimen282
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoreq
uick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoreo
bjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorep
athprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorea
rrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen283
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcores
hade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen284
\pgf@sys@shading@range@num=\count321
\pgf@shadingcount=\count322
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorei
mage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoree
xternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box64
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorel
ayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcoret
ransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorep
atterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcorer
df.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodulesh
apes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box65
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodulepl
ot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfcomp
-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen285
\pgf@nodesepend=\dimen286
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfcomp
-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgffor.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfkeys.sty

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys.c
ode.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/math\pgfmath.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code.t
ex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgffor.co
de.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen287
\pgffor@skip=\dimen288
\pgffor@stack=\toks45
\pgffor@toks=\toks46
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz\
tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibrar
yplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count323
\pgfplotmarksize=\dimen289
)
\tikz@lastx=\dimen290
\tikz@lasty=\dimen291
\tikz@lastxsaved=\dimen292
\tikz@lastysaved=\dimen293
\tikz@lastmovetox=\dimen294
\tikz@lastmovetoy=\dimen295
\tikzleveldistance=\dimen296
\tikzsiblingdistance=\dimen297
\tikz@figbox=\box66
\tikz@figbox@bg=\box67
\tikz@tempbox=\box68
\tikz@tempbox@bg=\box69
\tikztreelevel=\count324
\tikznumberofchildren=\count325
\tikznumberofcurrentchild=\count326
\tikz@fig@count=\count327

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodulema
trix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count328
\pgfmatrixcurrentcolumn=\count329
\pgf@matrix@numberofcolumns=\count330
)
\tikz@expandcount=\count331

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.code.
tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotscore.c
ode.tex
\t@pgfplots@toka=\toks47
\t@pgfplots@tokb=\toks48
\t@pgfplots@tokc=\toks49
\pgfplots@tmpa=\dimen298
\c@pgfplots@coordindex=\count332
\c@pgfplots@scanlineindex=\count333

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/sys\pgfplotssy
sgeneric.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\pgfplotsl
ibrary.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/oldpgfcompatib
\pgfplotsoldpgfsupp_loader.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibrar
yfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks50
\t@pgf@tokb=\toks51
\t@pgf@tokc=\toks52

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/oldpgfcompatib
\pgfplotsoldpgfsupp_pgfutil-common-lists.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplotsu
til.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructure\
pgfplotsliststructure.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructure\
pgfplotsliststructureext.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructure\
pgfplotsarray.code.tex
\c@pgfplotsarray@tmp=\count334
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructure\
pgfplotsmatrix.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/numtable\pgfpl
otstableshared.code.tex
\c@pgfplotstable@counta=\count335
\t@pgfplotstable@a=\toks53
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/liststructure\
pgfplotsdeque.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplotsb
inary.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplotsb
inary.data.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplotsu
til.verb.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\pgflibrar
ypgfplots.surfshading.code.tex
\c@pgfplotslibrarysurf@no=\count336

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/sys\pgflibrary
pgfplots.surfshading.pgfsys-pdftex.def)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplotsc
olormap.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/util\pgfplotsc
olor.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsstacke
dplots.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsplotha
ndlers.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsmeshpl
othandler.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsmeshpl
otimage.code.tex)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.scali
ng.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotscoordp
rocessing.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.error
bars.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.marke
rs.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplotsticks.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots\pgfplots.paths
.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibrarydecorations.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodulede
corations.code.tex
\pgfdecoratedcompleteddistance=\dimen299
\pgfdecoratedremainingdistance=\dimen300
\pgfdecoratedinputsegmentcompleteddistance=\dimen301
\pgfdecoratedinputsegmentremainingdistance=\dimen302
\pgf@decorate@distancetomove=\dimen303
\pgf@decorate@repeatstate=\count337
\pgfdecorationsegmentamplitude=\dimen304
\pgfdecorationsegmentlength=\dimen305
)
\tikz@lib@dec@box=\box70
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibrarydecorations.pathmorphing.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/decoratio
ns\pgflibrarydecorations.pathmorphing.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibrarydecorations.pathreplacing.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/decoratio
ns\pgflibrarydecorations.pathreplacing.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgfplots/libs\tikzlibra
rypgfplots.contourlua.code.tex)
\pgfplots@numplots=\count338
\pgfplots@xmin@reg=\dimen306
\pgfplots@xmax@reg=\dimen307
\pgfplots@ymin@reg=\dimen308
\pgfplots@ymax@reg=\dimen309
\pgfplots@zmin@reg=\dimen310
\pgfplots@zmax@reg=\dimen311
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibrar
yplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryshapes.code.tex
File: tikzlibraryshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\pg
flibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\pg
flibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryshapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\pg
flibraryshapes.symbols.code.tex
File: pgflibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryshapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\pg
flibraryshapes.arrows.code.tex
File: pgflibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryshapes.callouts.code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\pg
flibraryshapes.callouts.code.tex))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryshapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries/shapes\pg
flibraryshapes.multipart.code.tex
File: pgflibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodepartlowerbox=\box71
\pgfnodeparttwobox=\box72
\pgfnodepartthreebox=\box73
\pgfnodepartfourbox=\box74
\pgfnodeparttwentybox=\box75
\pgfnodepartnineteenbox=\box76
\pgfnodeparteighteenbox=\box77
\pgfnodepartseventeenbox=\box78
\pgfnodepartsixteenbox=\box79
\pgfnodepartfifteenbox=\box80
\pgfnodepartfourteenbox=\box81
\pgfnodepartthirteenbox=\box82
\pgfnodeparttwelvebox=\box83
\pgfnodepartelevenbox=\box84
\pgfnodeparttenbox=\box85
\pgfnodepartninebox=\box86
\pgfnodeparteightbox=\box87
\pgfnodepartsevenbox=\box88
\pgfnodepartsixbox=\box89
\pgfnodepartfivebox=\box90
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/libraries\pgflibrar
yarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen312
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/frontendlayer/tikz/
libraries\tikzlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvdefineke
ys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.s
ty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\gettitle
string.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count339
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count340
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen313
\Hy@linkcounter=\count341
\Hy@pagecounter=\count342
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count343

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count344

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen314

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigintcalc.s
ty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count345
\Field@Width=\dimen315
\Fld@charsize=\dimen316
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring ON on input line 6060.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count346
\c@Item=\count347
\c@Hfootnote=\count348
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count349
\c@bookmark@seq@number=\count350

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\rerunfilec
heck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uniquecou
nter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip97
)
\c@definition=\count351
\c@theorem=\count352
\c@lemma=\count353
\c@corollary=\count354
\c@proposition=\count355

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdfte
x.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count356
\l__pdf_internal_box=\box91
) (pgd.aux)
\openout1 = `pgd.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 80.
LaTeX Font Info:    ... okay on input line 80.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 80.
LaTeX Font Info:    ... okay on input line 80.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 80.
LaTeX Font Info:    ... okay on input line 80.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 80.
LaTeX Font Info:    ... okay on input line 80.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 80.
LaTeX Font Info:    ... okay on input line 80.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 80.
LaTeX Font Info:    ... okay on input line 80.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 80.
LaTeX Font Info:    ... okay on input line 80.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 80.
LaTeX Font Info:    ... okay on input line 80.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 80.
LaTeX Font Info:    ... okay on input line 80.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count357
\scratchdimen=\dimen317
\scratchbox=\box92
\nofMPsegments=\count358
\nofMParguments=\count359
\everyMPshowfont=\toks54
\MPscratchCnt=\count360
\MPscratchDim=\dimen318
\MPnumerator=\count361
\makeMPintoPDFobject=\count362
\everyMPtoPDFconversion=\toks55
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstopdf-bas
e.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: hyperref package is loaded.
Package caption Info: listings package is loaded.
Package caption Info: End \AtBeginDocument code.
\c@lstlisting=\count363

Package pgfplots notification 'compat/show suggested version=true': you might b
enefit from \pgfplotsset{compat=1.18} (current compat level: 1.17).

Package hyperref Info: Link coloring ON on input line 80.
(pgd.out) (pgd.out)
\@outlinefile=\write4
\openout4 = `pgd.out'.



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]


pdfTeX warning (ext4): destination with the same identifier (name{page.i}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.125 \newpage
               [1]

[2]

[3]
LaTeX Font Info:    Trying to load font information for U+msa on input line 150
.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 150
.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+wasy on input line 15
0.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/wasysym\uwasy.fd
File: uwasy.fd 2020/01/19 v2.4 Wasy-2 symbol font definitions
)

[4] (pgd.toc

[5]

[6]

[7]

[8])
\tf@toc=\write5
\openout5 = `pgd.toc'.



[9] (pgd.lot)
\tf@lot=\write6
\openout6 = `pgd.lot'.



[10] (pgd.lof)
\tf@lof=\write7
\openout7 = `pgd.lof'.



[11] (pgd.loa)
\tf@loa=\write8
\openout8 = `pgd.loa'.



[12

]

[13]
Chapter 1.


[1

]
Overfull \hbox (12.00089pt too wide) in paragraph at lines 231--232
[]\OT1/cmr/m/n/12 The in-te-gra-tion of ar-ti-fi-cial in-tel-li-gence into sup-
ply chain man-age-ment rep-re-sents a paradigm
 []


Overfull \hbox (8.44121pt too wide) in paragraph at lines 237--238
\OT1/cmr/m/n/12 Despite widespread aware-ness of ad-vanced an-a-lyt-ics ca-pa-b
il-i-ties, many or-ga-ni-za-tions|from
 []



[2]
Overfull \vbox (4.07616pt too high) has occurred while \output is active []



[3]
Overfull \vbox (0.14256pt too high) has occurred while \output is active []



[4]

[5]

[6]

[7]

[8]

[9]
Chapter 2.

Overfull \vbox (3.39261pt too high) has occurred while \output is active []



[10

]

[11]
Overfull \vbox (0.77312pt too high) has occurred while \output is active []



[12]
Overfull \hbox (17.44986pt too wide) in paragraph at lines 443--443
|[]\OT1/cmr/bx/n/17.28 Emergence of Pre-dic-tive An-a-lyt-ics in Sup-ply Chain|
 
 []


Overfull \hbox (5.53845pt too wide) in paragraph at lines 447--448
\OT1/cmr/m/n/12 The tran-si-tion from de-ter-min-is-tic to prob-a-bilis-tic pla
n-ning be-gan with ex-po-nen-tial smooth-
 []


Overfull \vbox (1.55923pt too high) has occurred while \output is active []



[13]

[14]

[15]

[16]

[17]
Overfull \hbox (3.93793pt too wide) in paragraph at lines 576--576
|[]\OT1/cmr/bx/n/17.28 Performance Met-rics and Eval-u-a-tion Frame-works| 
 []



[18]

[19]

[20]

[21]
Overfull \vbox (2.80101pt too high) has occurred while \output is active []



[22]

[23]
Overfull \hbox (18.64536pt too wide) in paragraph at lines 711--712
[]\OT1/cmr/m/n/12 Human-AI col-lab-o-ra-tion con-sis-tently out-per-forms full 
au-toma-tion for strate-gic decision-
 []



[24]
Chapter 3.

Overfull \vbox (4.0593pt too high) has occurred while \output is active []



[25

]

[26]

[27]

[28]

[29]

[30]

[31]

[32]

[33]

[34]

[35]
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstlang1.sty
File: lstlang1.sty 2024/09/23 1.10c listings language file
)
Package hyperref Info: bookmark level for unknown lstlisting defaults to 0 on i
nput line 1123.


[36]
Package hyperref Info: bookmark level for unknown algorithm defaults to 0 on in
put line 1188.


[37]

[38]

[39]

[40]

[41]

[42]

[43]

[44]

[45]
Chapter 4.


[46

]

[47]

[48]

[49]

[50]

[51]
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstlang1.sty
File: lstlang1.sty 2024/09/23 1.10c listings language file
)

[52]

[53]

[54]

[55]

[56]

[57]

[58]

[59]

[60]
Overfull \hbox (62.77547pt too wide) in paragraph at lines 2205--2218
 [][] 
 []



[61]

[62]

[63]

[64]

[65]

[66]

[67]

[68]

[69]
Chapter 5.


[70

]

[71]

[72]

[73]

[74]

[75]

[76]

[77]

[78]

[79]

[80]

[81

]

[82

]

[83]

[84]

[85]

[86]

[87]
Appendix A.


[88

]

[89]

[90]
Overfull \hbox (257.86688pt too wide) in paragraph at lines 3146--3161
 [][] 
 []



[91]

[92]
Appendix B.


[93

]

[94]

[95]
Appendix C.


[96

]

[97]

[98]

[99]

[100]

[101]

[102]

[103]

[104]

[105]

[106]

[107]

[108]
Appendix D.


[109

]
Overfull \vbox (74.81827pt too high) has occurred while \output is active []



[110]
Overfull \hbox (17.14665pt too wide) in paragraph at lines 3828--3840
 [][] 
 []


Overfull \hbox (210.00444pt too wide) in paragraph at lines 3866--3878
 [][] 
 []



[111]
Overfull \hbox (106.52542pt too wide) in paragraph at lines 3887--3897
 [][] 
 []



[112]
Appendix E.


[113

]

[114]

[115]

[116]

[117]

[118]
Appendix F.

Overfull \hbox (5.5922pt too wide) in paragraph at lines 4207--4208
[]\OT1/cmr/m/n/12 Integrated soft-ware sys-tems that man-age busi-
 []



[119

]

[120]

[121]

[122] (pgd.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
Package rerunfilecheck Info: File `pgd.out' has not changed.
(rerunfilecheck)             Checksum: 9E4F2AEFDDD2F5F2FEE3E8290565CC7F;34912.
 ) 
Here is how much of TeX's memory you used:
 40704 strings out of 473523
 918834 string characters out of 5715562
 2167157 words of memory out of 5000000
 61651 multiletter control sequences out of 15000+600000
 571233 words of font info for 84 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 99i,11n,101p,825b,2046s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/public/wasy/dpi720\wasy10
.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tcbx
1200.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\
tctt1000.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi
600\tcrm1200.pk><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/
amsfonts/cm/cmbx10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/
public/amsfonts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts
/type1/public/amsfonts/cm/cmex10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTe
X/fonts/type1/public/amsfonts/cm/cmmi12.pfb><C:/Users/<USER>/AppData/Local/Program
s/MiKTeX/fonts/type1/public/amsfonts/cm/cmmi8.pfb><C:/Users/<USER>/AppData/Local/P
rograms/MiKTeX/fonts/type1/public/amsfonts/cm/cmr10.pfb><C:/Users/<USER>/AppData/L
ocal/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr12.pfb><C:/Users/<USER>/App
Data/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr6.pfb><C:/Users/<USER>
P/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr7.pfb><C:/Use
rs/H P/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmr8.pfb><C
:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy10
.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm
/cmsy6.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfo
nts/cm/cmsy8.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public
/amsfonts/cm/cmti10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1
/public/amsfonts/cm/cmti12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/font
s/type1/public/amsfonts/cm/cmtt10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKT
eX/fonts/type1/public/amsfonts/cm/cmtt12.pfb><C:/Users/<USER>/AppData/Local/Progra
ms/MiKTeX/fonts/type1/public/amsfonts/symbols/msam10.pfb><C:/Users/<USER>/AppData/
Local/Programs/MiKTeX/fonts/type1/public/amsfonts/symbols/msbm10.pfb>
Output written on pgd.pdf (136 pages, 937958 bytes).
PDF statistics:
 3388 PDF objects out of 3580 (max. 8388607)
 1679 named destinations out of 1728 (max. 500000)
 1429 words of extra memory for PDF output out of 10000 (max. 10000000)

