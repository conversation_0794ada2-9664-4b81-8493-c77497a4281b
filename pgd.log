This is pdfTeX, Version 3.141592653-2.6-1.40.27 (MiKTeX 25.3) (preloaded format=pdflatex 2025.3.13)  30 JUL 2025 17:05
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./pgd.tex
(pgd.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\report.cls
Document Class: report 2024/06/29 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size12.clo
File: size12.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@chapter=\count197
\c@section=\count198
\c@subsection=\count199
\c@subsubsection=\count266
\c@paragraph=\count267
\c@subparagraph=\count268
\c@figure=\count269
\c@table=\count270
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count271
\Gm@cntv=\count272
\c@Gm@tempcnt=\count273
\Gm@bindingoffset=\dimen142
\Gm@wd@mp=\dimen143
\Gm@odd@mp=\dimen144
\Gm@even@mp=\dimen145
\Gm@layoutwidth=\dimen146
\Gm@layoutheight=\dimen147
\Gm@layouthoffset=\dimen148
\Gm@layoutvoffset=\dimen149
\Gm@dimlist=\toks18

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/setspace\setspace.sty
Package: setspace 2022/12/04 v6.7b set line spacing
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box52
\beforetitleunit=\skip51
\aftertitleunit=\skip52
\ttl@plus=\dimen150
\ttl@minus=\dimen151
\ttl@toksa=\toks19
\titlewidth=\dimen152
\titlewidthlast=\dimen153
\titlewidthfirst=\dimen154
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tocloft\tocloft.sty
Package: tocloft 2017/08/31 v2.3i parameterised ToC, etc., typesetting
Package tocloft Info: The document has chapter divisions on input line 51.
\cftparskip=\skip53
\cftbeforetoctitleskip=\skip54
\cftaftertoctitleskip=\skip55
\cftbeforepartskip=\skip56
\cftpartnumwidth=\skip57
\cftpartindent=\skip58
\cftbeforechapskip=\skip59
\cftchapindent=\skip60
\cftchapnumwidth=\skip61
\cftbeforesecskip=\skip62
\cftsecindent=\skip63
\cftsecnumwidth=\skip64
\cftbeforesubsecskip=\skip65
\cftsubsecindent=\skip66
\cftsubsecnumwidth=\skip67
\cftbeforesubsubsecskip=\skip68
\cftsubsubsecindent=\skip69
\cftsubsubsecnumwidth=\skip70
\cftbeforeparaskip=\skip71
\cftparaindent=\skip72
\cftparanumwidth=\skip73
\cftbeforesubparaskip=\skip74
\cftsubparaindent=\skip75
\cftsubparanumwidth=\skip76
\cftbeforeloftitleskip=\skip77
\cftafterloftitleskip=\skip78
\cftbeforefigskip=\skip79
\cftfigindent=\skip80
\cftfignumwidth=\skip81
\c@lofdepth=\count274
\c@lotdepth=\count275
\cftbeforelottitleskip=\skip82
\cftafterlottitleskip=\skip83
\cftbeforetabskip=\skip84
\cfttabindent=\skip85
\cfttabnumwidth=\skip86
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/endnotes\endnotes.sty
Package: endnotes 2020-01-02 endnotes package
\c@endnote=\count276
\endnotesep=\dimen155
\@enotes=\write3
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2024/11/05 v2.17t AMS math features
\@mathmargin=\skip87

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks20
\ex@=\dimen156
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen157
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count277
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count278
\leftroot@=\count279
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count280
\DOTSCASE@=\count281
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen158
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count282
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count283
\dotsspace@=\muskip17
\c@parentequation=\count284
\dspbrk@lvl=\count285
\tag@help=\toks21
\row@=\count286
\column@=\count287
\maxfields@=\count288
\andhelp@=\toks22
\eqnshift@=\dimen159
\alignsep@=\dimen160
\tagshift@=\dimen161
\tagwidth@=\dimen162
\totwidth@=\dimen163
\lineht@=\dimen164
\@envbody=\toks23
\multlinegap=\skip88
\multlinetaggap=\skip89
\mathdisplay@stack=\toks24
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen165
\Gin@req@width=\dimen166
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/caption\caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen167
\captionmargin=\dimen168
\caption@leftmargin=\dimen169
\caption@rightmargin=\dimen170
\caption@width=\dimen171
\caption@indent=\dimen172
\caption@parindent=\dimen173
\caption@hangindent=\dimen174
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count289
\c@continuedfloat=\count290
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count291
\float@exts=\toks25
\float@box=\box55
\@float@everytoks=\toks26
\@floatcapt=\box56
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen175
\lightrulewidth=\dimen176
\cmidrulewidth=\dimen177
\belowrulesep=\dimen178
\belowbottomsep=\dimen179
\aboverulesep=\dimen180
\abovetopsep=\dimen181
\cmidrulesep=\dimen182
\cmidrulekern=\dimen183
\defaultaddspace=\dimen184
\@cmidla=\count292
\@cmidlb=\count293
\@aboverulesep=\dimen185
\@belowrulesep=\dimen186
\@thisruleclass=\count294
\@lastruleclass=\count295
\@thisrulewidth=\dimen187
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip90
\multirow@cntb=\count296
\multirow@dima=\skip91
\bigstrutjot=\dimen188
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\listings.sty
\lst@mode=\count297
\lst@gtempboxa=\box57
\lst@token=\toks27
\lst@length=\count298
\lst@currlwidth=\dimen189
\lst@column=\count299
\lst@pos=\count300
\lst@lostspace=\dimen190
\lst@width=\dimen191
\lst@newlines=\count301
\lst@lineno=\count302
\lst@maxwidth=\dimen192

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstpatch.sty
File: lstpatch.sty 2024/09/23 1.10c (Carsten Heinz)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstmisc.sty
File: lstmisc.sty 2024/09/23 1.10c (Carsten Heinz)
\c@lstnumber=\count303
\lst@skipnumbers=\count304
\lst@framebox=\box58
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\listings.cfg
File: listings.cfg 2024/09/23 1.10c listings configuration
))
Package: listings 2024/09/23 1.10c (Carsten Heinz)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvdefineke
ys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds.s
ty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\gettitle
string.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count305
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count306
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/stringenc\stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\@linkdim=\dimen193
\Hy@linkcounter=\count307
\Hy@pagecounter=\count308
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count309

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `colorlinks' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count310

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen194

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigintcalc.s
ty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count311
\Field@Width=\dimen195
\Fld@charsize=\dimen196
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring ON on input line 6060.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count312
\c@Item=\count313
\c@Hfootnote=\count314
)
Package hyperref Info: Driver (autodetected): hpdftex.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count315
\c@bookmark@seq@number=\count316

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\rerunfilec
heck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uniquecou
nter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip92
)
! Undefined control sequence.
l.27 \definecolor
                 {codegreen}{rgb}{0,0.6,0}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


! LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.27 \definecolor{c
                   odegreen}{rgb}{0,0.6,0}
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

! Undefined control sequence.
l.28 \definecolor
                 {codegray}{rgb}{0.5,0.5,0.5}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

! Undefined control sequence.
l.29 \definecolor
                 {backcolour}{rgb}{0.95,0.95,0.92}
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdfte
x.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count317
\l__pdf_internal_box=\box59
) (pgd.aux)
\openout1 = `pgd.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 55.
LaTeX Font Info:    ... okay on input line 55.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 55.
LaTeX Font Info:    ... okay on input line 55.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 55.
LaTeX Font Info:    ... okay on input line 55.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 55.
LaTeX Font Info:    ... okay on input line 55.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 55.
LaTeX Font Info:    ... okay on input line 55.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 55.
LaTeX Font Info:    ... okay on input line 55.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 55.
LaTeX Font Info:    ... okay on input line 55.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 55.
LaTeX Font Info:    ... okay on input line 55.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 55.
LaTeX Font Info:    ... okay on input line 55.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count318
\scratchdimen=\dimen197
\scratchbox=\box60
\nofMPsegments=\count319
\nofMParguments=\count320
\everyMPshowfont=\toks28
\MPscratchCnt=\count321
\MPscratchDim=\dimen198
\MPnumerator=\count322
\makeMPintoPDFobject=\count323
\everyMPtoPDFconversion=\toks29
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstopdf-bas
e.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: float package is loaded.
Package caption Info: hyperref package is loaded.
Package caption Info: listings package is loaded.
Package caption Info: End \AtBeginDocument code.
\c@lstlisting=\count324
 (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\color.sty
Package: color 2024/06/23 v1.3e Standard LaTeX Color (DPC)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx))
Package hyperref Info: Link coloring ON on input line 55.

(pgd.out) (pgd.out)
\@outlinefile=\write4
\openout4 = `pgd.out'.



[1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]


pdfTeX warning (ext4): destination with the same identifier (name{page.i}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.78 \end{titlepage}
                     [1]


pdfTeX warning (ext4): destination with the same identifier (name{page.i}) has 
been already used, duplicate ignored
<to be read again> 
                   \relax 
l.100 \newpage
               [1]

[2]

[3]

[4] (pgd.toc
LaTeX Font Info:    Trying to load font information for U+msa on input line 1.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 1.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)

[5]

[6]

[7]

[8])
\tf@toc=\write5
\openout5 = `pgd.toc'.



[9] (pgd.lot)
\tf@lot=\write6
\openout6 = `pgd.lot'.



[10] (pgd.lof)
\tf@lof=\write7
\openout7 = `pgd.lof'.



[11]

[12]
Chapter 1.
\openout3 = `pgd.ent'.



[1

]

[2]

[3]

[4]
Chapter 2.


[5

]

[6]

[7]

[8]

[9]
Chapter 3.


[10

]

[11]

[12]

[13]

[14]

[15]

[16]
Chapter 4.

LaTeX Warning: Reference `tab:product_categories' on page 17 undefined on input
 line 648.



[17

]
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstlang1.sty
File: lstlang1.sty 2024/09/23 1.10c listings language file
)
Package hyperref Info: bookmark level for unknown lstlisting defaults to 0 on i
nput line 694.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.694 ...caption=Basic Forecasting Implementation]
                                                  
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.694 ...caption=Basic Forecasting Implementation]
                                                  
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.694 ...caption=Basic Forecasting Implementation]
                                                  
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    Font shape `OT1/cmtt/bx/n' in size <10> not available
(Font)              Font shape `OT1/cmtt/m/n' tried instead on input line 695.


[18]

[19]

[20]
Overfull \hbox (24.32396pt too wide) detected at line 819
[][][]\OML/cmm/m/it/12 ROI \OT1/cmr/m/n/12 = [] \OMS/cmsy/m/n/12 ^^B \OT1/cmr/m
/n/12 100% = [] \OMS/cmsy/m/n/12 ^^B \OT1/cmr/m/n/12 100% = 241%
 []



[21]

[22]

[23]
Chapter 5.


[24

]

[25]

[26]

[27]

[28]

[29]

[30]

[31]

! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1158 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1158 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1158 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1163 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1163 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1163 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1168 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1168 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1168 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1173 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1173 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


! LaTeX Error: Undefined color `backcolour'.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.1173 \begin{lstlisting}
                         
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.



[32]

[33] (pgd.ent)

[34]

[35] (pgd.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********


LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.


Package rerunfilecheck Warning: File `pgd.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `pgd.out':
(rerunfilecheck)             Before: 9E4F2AEFDDD2F5F2FEE3E8290565CC7F;34912
(rerunfilecheck)             After:  A92B2CD6AFDB9C23D89D8E8EAD150C68;16760.
 ) 
Here is how much of TeX's memory you used:
 14835 strings out of 473523
 229426 string characters out of 5715562
 881748 words of memory out of 5000000
 37563 multiletter control sequences out of 15000+600000
 567581 words of font info for 68 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 75i,8n,79p,472b,2142s stack positions out of 10000i,1000n,20000p,200000b,200000s

pdfTeX warning (dest): name{figure.caption.53} has been referenced but does not
 exist, replaced by a fixed one


pdfTeX warning (dest): name{figure.caption.24} has been referenced but does not
 exist, replaced by a fixed one


pdfTeX warning (dest): name{figure.caption.10} has been referenced but does not
 exist, replaced by a fixed one


pdfTeX warning (dest): name{figure.caption.9} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.69} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.68} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.67} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.66} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.65} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.64} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.63} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.62} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.61} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.55} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.51} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.49} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.47} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.45} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.44} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.43} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.42} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.41} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.40} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.37} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{table.caption.35} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{appendix.F} has been referenced but does not exist,
 replaced by a fixed one


pdfTeX warning (dest): name{subsection.E.3.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.E.3.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.E.3.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.E.3} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{subsection.E.2.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.E.2.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.E.2.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.E.2} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{subsection.E.1.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.E.1.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.E.1} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{appendix.E} has been referenced but does not exist,
 replaced by a fixed one


pdfTeX warning (dest): name{subsection.D.4.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.D.4.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.D.4} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{section.D.3} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{subsection.D.2.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.D.2.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.D.2} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{section.D.1} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{appendix.D} has been referenced but does not exist,
 replaced by a fixed one


pdfTeX warning (dest): name{section.C.2} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{section.C.1} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{appendix.C} has been referenced but does not exist,
 replaced by a fixed one


pdfTeX warning (dest): name{subsection.B.1.7} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.B.1.6} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.B.1.5} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.B.1.4} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.B.1.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.B.1.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.B.1.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.B.1} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{appendix.B} has been referenced but does not exist,
 replaced by a fixed one


pdfTeX warning (dest): name{subsection.A.1.5} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.A.1.4} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.A.1.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.A.1.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.A.1.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.A.1} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{appendix.A} has been referenced but does not exist,
 replaced by a fixed one


pdfTeX warning (dest): name{chapter*.57} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{subsection.4.5.4} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.4.5.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.4.4.7} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.4.4.6} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.4.4.5} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.4.4.4} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.4.4.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.3.8.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.3.6.4} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.3.3.4} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.3.3.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.3.3.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.3.2.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.3.2.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.3.2.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.2.12} has been referenced but does not exis
t, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.11.2} has been referenced but does not
 exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.11.1} has been referenced but does not
 exist, replaced by a fixed one


pdfTeX warning (dest): name{section.2.11} has been referenced but does not exis
t, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.10.2} has been referenced but does not
 exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.10.1} has been referenced but does not
 exist, replaced by a fixed one


pdfTeX warning (dest): name{section.2.10} has been referenced but does not exis
t, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.9.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.9.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.9.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.2.9} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.8.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.8.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.8.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.2.8} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.7.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.7.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.6.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.6.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.6.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.5.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.4.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.2.2.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{section.1.8} has been referenced but does not exist
, replaced by a fixed one


pdfTeX warning (dest): name{subsection.1.6.5} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.1.6.4} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.1.6.3} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.1.6.2} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{subsection.1.6.1} has been referenced but does not 
exist, replaced by a fixed one


pdfTeX warning (dest): name{chapter*.1} has been referenced but does not exist,
 replaced by a fixed one

 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tcrm1200
.pk><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/
cmbx10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfo
nts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/publi
c/amsfonts/cm/cmex10.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type
1/public/amsfonts/cm/cmmi12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fon
ts/type1/public/amsfonts/cm/cmmi8.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKT
eX/fonts/type1/public/amsfonts/cm/cmr10.pfb><C:/Users/<USER>/AppData/Local/Program
s/MiKTeX/fonts/type1/public/amsfonts/cm/cmr12.pfb><C:/Users/<USER>/AppData/Local/P
rograms/MiKTeX/fonts/type1/public/amsfonts/cm/cmr8.pfb><C:/Users/<USER>/AppData/Lo
cal/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy10.pfb><C:/Users/<USER>/App
Data/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmsy8.pfb><C:/Users/<USER>
 P/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmti10.pfb><C:/
Users/H P/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/cmti12.p
fb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/amsfonts/cm/c
mtt10.pfb>
Output written on pgd.pdf (49 pages, 417027 bytes).
PDF statistics:
 1353 PDF objects out of 1440 (max. 8388607)
 318 named destinations out of 1000 (max. 500000)
 1417 words of extra memory for PDF output out of 10000 (max. 10000000)

